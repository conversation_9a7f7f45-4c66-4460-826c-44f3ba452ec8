ESX = nil

TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

RegisterServerEvent('esx_skin:save')
AddEventHandler('esx_skin:save', function(skin)
  local xPlayer = ESX.GetPlayerFromId(source)

  exports.ghmattimysql:execute(
    'UPDATE users SET `skin` = @skin WHERE identifier = @identifier',
    {
      ['@skin']       = json.encode(skin),
      ['@identifier'] = xPlayer.identifier
    }
  )
end)

RegisterServerEvent('esx_skin:responseSaveSkin')
AddEventHandler('esx_skin:responseSaveSkin', function(skin)
  local file = io.open('resources/[esx]/esx_skin/skins.txt', "a")

  file:write(json.encode(skin) .. "\n\n")
  file:flush()
  file:close()
end)

-- Commands
RegisterCommand('skin', function(source, args)
  local xPlayer = ESX.GetPlayerFromId(source)
  local target = tonumber(args[1])
  if xPlayer.permission_level >= 3 then
    if xPlayer.get('aduty') then
      if args[1] then
        TriggerClientEvent('esx_skin:openSaveableMenu', target)
      else
        TriggerClientEvent('esx_skin:openSaveableMenu', source)
      end
    else
      TriggerClientEvent('esx:showNotification', source, "Shoma Off Duty Mibashid")
    end
  end
end, false)

RegisterCommand('saveskin', function(source)
  local xPlayer = ESX.GetPlayerFromId(source)
  if xPlayer.permission_level > 8 then
    TriggerClientEvent('esx_skin:requestSaveSkin', source)
  else
    TriggerClientEvent('chatMessage', source, "[ System ] ", { 255, 0, 0 },
      " ^0Shoma dastresi kafi baraye esfade az in dastor ra nadarid!")
  end
end, false)

RegisterCommand('changevest', function(source, args)
  local xPlayer = ESX.GetPlayerFromId(source)
  if xPlayer.permission_level > 8 then
    if args[1] and args[2] then
      if tonumber(args[1]) and tonumber(args[2]) then
        local skinone, skintwo = tonumber(args[1]), tonumber(args[2])
        TriggerClientEvent('esx_skin:changeVest', source, skinone, skintwo)
      else
        TriggerClientEvent('chatMessage', source, "[ System ] ", { 255, 0, 0 },
          " ^0Shoma Dar Ghesmat Value Faghat Mitavanid Adad Vared Konid!")
      end
    else
      TriggerClientEvent('chatMessage', source, "[ System ] ", { 255, 0, 0 }, " ^0Syntax Vared Shode Eshbteh Ast!")
    end
  else
    TriggerClientEvent('chatMessage', source, "[ System ] ", { 255, 0, 0 },
      " ^0Shoma Dastresi Kafi Barai Estefade Az In Cmd Ra Nadarid!")
  end
end, false)

ESX.RegisterServerCallback('esx_skin:getGangSkin', function(source, cb)
  local xPlayer = ESX.GetPlayerFromId(source)

  exports.ghmattimysql:scalar('SELECT skin FROM users WHERE identifier = @identifier', {
    ['@identifier'] = xPlayer.identifier
  }, function(skin)
    local gangSkin = {
      skin_male = xPlayer.gang.skin_male,
      skin_female = xPlayer.gang.skin_female
    }

    if skin ~= nil then
      skin = json.decode(skin)
    end

    cb(skin, gangSkin)
  end)
end)
