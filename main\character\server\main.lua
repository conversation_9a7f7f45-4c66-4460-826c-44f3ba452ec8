ESX = nil

TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

local ReservedBuckets = 0

RegisterServerEvent("Character:CharacterCreationStarted")
AddEventHandler("Character:CharacterCreationStarted", function()
    ReservedBuckets = ReservedBuckets + 1
    SetPlayerRoutingBucket(source, ReservedBuckets)
end)

RegisterServerEvent("Character:CharacterCreationEnded")
AddEventHandler("Character:CharacterCreationEnded", function()
    SetPlayerRoutingBucket(source, 0)
end)

ESX.RegisterServerCallback('CheckMe', function(source, cb)
	local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
	MySQL.Async.fetchAll('SELECT * FROM users WHERE identifier=@identifier', 
    {
        ['@identifier'] =  GetPlayerIdentifier(source)

    }, function(data)
        if not data[1] or not data[1].playerName or data[1].playerName == "" or data[1].playerName == nil then
            cb(true)
        else
            cb(false)
        end
    end)
end)