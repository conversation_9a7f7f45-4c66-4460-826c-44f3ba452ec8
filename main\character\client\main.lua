local cam = nil
local continuousFadeOutNetwork = false
local needAskQuestions, needRegister
local firstSpawn, disableAttack = true, true
ESX = nil
local risdead = false
Citizen.CreateThread(function()
	while ESX == nil do
		TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
		Citizen.Wait(0)
	end
end)
function f(n)
	n = n + 0.00000
	return n
end

RegisterNetEvent('registerForm')
AddEventHandler('registerForm', function(bool)
	needRegister = bool
end)

function setCamHeight(height)
	local pos = GetEntityCoords(PlayerPedId())
	SetCamCoord(cam,vector3(pos.x,pos.y,f(height)))
end

local function StartFade()
	DoScreenFadeOut(500)
	while IsScreenFadingOut() do
		Citizen.Wait(1)
	end
end

local function EndFade()
	ShutdownLoadingScreen()
	DoScreenFadeIn(500)
	while IsScreenFadingIn() do
		Citizen.Wait(1)
	end
end

function DisalbeAttack()
	DisableControlAction(0, 19, true) 
	DisableControlAction(0, 45, true)
	DisableControlAction(0, 24, true) 
	DisableControlAction(0, 257, true)
	DisableControlAction(0, 25, true) 
	DisableControlAction(0, 68, true)
	DisableControlAction(0, 69, true)
	DisableControlAction(0, 70, true) 
	DisableControlAction(0, 92, true) 
	DisableControlAction(0, 346, true) 
	DisableControlAction(0, 347, true) 
	DisableControlAction(0, 264, true) 
	DisableControlAction(0, 257, true) 
	DisableControlAction(0, 140, true) 
	DisableControlAction(0, 141, true) 
	DisableControlAction(0, 142, true) 
	DisableControlAction(0, 143, true) 
	DisableControlAction(0, 263, true) 
	if disableAttack then
		SetTimeout(0, function ()
			DisalbeAttack()
		end)
	end
end

local PlayerLoadCoords

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
	if xPlayer.lastPosition ~= nil or xPlayer.lastPosition ~= "null" then 
		PlayerLoadCoords = xPlayer.lastPosition
	end
end)

AddEventHandler('playerSpawned', function()
	if not firstSpawn then return end
	firstSpawn = false
	StartUpLoading()
end)

function showLoadingPromt(label, time)
    Citizen.CreateThread(function()
        BeginTextCommandBusyString(tostring(label))
        EndTextCommandBusyString(3)
        Citizen.Wait(time)
        RemoveLoadingPrompt()
    end)
end

function ReadToPlay()
	for i = 1, 3 do
		SetEntityCoords(PlayerPedId(), PlayerLoadCoords.x+0.0, PlayerLoadCoords.y+0.0, PlayerLoadCoords.z -1+3.0)
	end
	TriggerEvent('es_admin:freezePlayer', true)
	disableAttack = false
	CameraLoadToGround()
	SetEntityInvincible(PlayerPedId(),false)
	SetEntityVisible(PlayerPedId(),true)
	FreezeEntityPosition(PlayerPedId(),false)
	SetPedDiesInWater(PlayerPedId(),true)
	DisplayRadar(true)
	KillCamera()
	TriggerEvent('esx:restoreLoadout')
	TriggerEvent('esx_status:setLastStats')
	TriggerServerEvent('esx_rack:loaded')
	Wait(500)
	TriggerEvent('esx_jail:CheckForJail')
    TriggerEvent("PlayerLoadedToGround")
	TriggerEvent('es_admin:freezePlayer', false)
	TriggerEvent('esx:playerSpawned')
	Wait(3000)
	TriggerEvent('Checkcombat')
	ESX.TriggerServerCallback('PNG_Comserv:IsInComServ', function(IsJailed)
		if IsJailed == false then
			Wait(100)
		else
			TriggerEvent('PNG_Comserv:TimeToFingerYourSelf', tonumber(IsJailed))
			ESX.ShowNotification("~o~~h~Hengami Ke Dar Server Naboodid Comserv Shodid Ya Dar Zendan DC Dadid Va Alan Shoma Be Zendan Enteqal Dade Mishid !!")
		end
	end)
	ESX.SetPlayerData('IsPlayerLoaded', 1)
	TriggerEvent('esx_best:checkVanish')
	ESX.SetPlayerData('IsLoaded', 1)	
end

function StartUpLoading()
	Citizen.CreateThread(function()
		StartFade()
		ShutdownLoadingScreen()
		ShutdownLoadingScreenNui()
		showLoadingPromt("MP_SPINLOADING", 500000)
		CreateCameraOnTop()
		SetEntityVisible(PlayerPedId(),true)
		DisalbeAttack()
		SetEntityInvincible(PlayerPedId(),true)
		FreezeEntityPosition(PlayerPedId(),true)
		SetPedDiesInWater(PlayerPedId(),false)
		DisplayRadar(false)
		Wait(10000)
		EndFade()
		DoScreenFadeIn(500)
		while needRegister == nil do
			Wait(1000)
		end
		if needRegister then
			ToggleHud()
			showLoadingPromt("MP_SPINLOADING", 0)
			showLoadingPromt("PCARD_JOIN_GAME", 500000)
			Wait(10000)
			showLoadingPromt("PCARD_JOIN_GAME", 0)
			CreateCameraOnTop()
			SetTimeout(1000,function()
				TriggerCreateCharacter()
			end)
		else
			TriggerEvent('freezePlayer', true)
			showLoadingPromt("MP_SPINLOADING", 0)
			showLoadingPromt("PCARD_JOIN_GAME", 500000)
			Wait(3000)
			showLoadingPromt("PCARD_JOIN_GAME", 0)
			CreateCameraOnTop()
			EndFade()
			ReadToPlay()
		end
	end)
end

function CreateCameraOnTop()
	if not DoesCamExist(cam) then
		cam = CreateCam("DEFAULT_SCRIPTED_CAMERA",false)
	end
	local pos = GetEntityCoords(PlayerPedId())
	SetCamCoord(cam,vector3(pos.x,pos.y,f(1000)))
	SetCamRot(cam,-f(90),f(0),f(0),2)
	SetCamActive(cam,true)
	StopCamPointing(cam)
	RenderScriptCams(true,true,0,0,0,0)
end

function CameraLoadToGround()
	if not DoesCamExist(cam) then
		cam = CreateCam("DEFAULT_SCRIPTED_CAMERA",false)
	end
	local altura = 1000
	local pos = GetEntityCoords(PlayerPedId())
	SetCamCoord(cam,vector3(pos.x,pos.y,f(1000)))
	while altura > (pos.z - 5.0) do
		if altura <= 300 then
			altura = altura - 6
		elseif altura >= 301 and altura <= 700 then
			altura = altura - 4
		else
			altura = altura - 2
		end
		setCamHeight(altura)
		Citizen.Wait(10)
	end
end

function KillCamera()
	if not DoesCamExist(cam) then
		cam = CreateCam("DEFAULT_SCRIPTED_CAMERA",false)
	end
	SetCamActive(cam,false)
	StopCamPointing(cam)
	RenderScriptCams(0,0,0,0,0,0)
	SetFocusEntity(PlayerPedId())
end

function CreateCharacterCamera()
	if not DoesCamExist(cam) then
		cam = CreateCam("DEFAULT_SCRIPTED_CAMERA",false)
	end
	SetCamCoord(cam,vector3(402.6,-997.2,-98.3))
	SetCamRot(cam,f(0),f(0),f(358),15)
	SetCamActive(cam,true)
	RenderScriptCams(true,true,20000000000000000000000000,0,0,0)
end

local isInCharacterMode = false
local currentCharacterMode = { sex = 0, dad = 0, mom = 0, skin_md_weight = 0, face_md_weight = 0.0, eye_color = 0, eyebrows_5 = 0, eyebrows_6 = 0, nose_1 = 0, nose_2 = 0, nose_3 = 0, nose_4 = 0, nose_5 = 0, nose_6 = 0, cheeks_1 = 0, cheeks_2 = 0, cheeks_3 = 0, lip_thickness = 0, jaw_1 = 0, jaw_2 = 0, chin_1 = 0, chin_2 = 0, chin_3 = 0, chin_4 = 0, neck_thickness = 0, hair_1 = 4, hair_2 = 0, hair_color_1 = 0, hair_color_2 = 0, eyebrows_1 = 0, eyebrows_1 = 10, eyebrows_3 = 0, eyebrows_4 = 0, beard_1 = -1, beard_2 = 10, beard_3 = 0, beard_4 = 0, chest_1 = -1, chest_1 = 10, chest_3 = 0, blush_1 = -1, blush_2 = 10, blush_3 = 0, lipstick_1 = -1, lipstick_2 = 10, lipstick_3 = 0, lipstick_4 = 0, blemishes_1 = -1, blemishes_2 = 10, age_1 = -1, age_2 = 10, complexion_1 = -1, complexion_2 = 10, sun_1 = -1, sun_2 = 10, moles_1 = -1, moles_2 = 10, makeup_1 = -1 , makeup_2 = 10, makeup_3 = 0 , makeup_4 = 0 }
local characterNome = ""
local characterSobrenome = ""

RegisterNetEvent('showRegisterForm')
AddEventHandler('showRegisterForm', function ()
	lastcoord = GetEntityCoords(PlayerPedId())
	needRegister = true
	StartUpLoading()
	ToggleHud()
end)
function ToggleHud()
	Citizen.Wait(4000)
	ExecuteCommand('togglehud 0')
end
function TriggerCreateCharacter()
	CreateCameraOnTop()
	isInCharacterMode = true
	StartFade()
	continuousFadeOutNetwork = true
	FadeOutNet()
	changeGender("mp_m_freemode_01")
	SetEntityVisible(PlayerPedId(),true)
	refreshDefaultCharacter()
	TaskUpdateSkinOptions()
	TaskUpdateFaceOptions()
	TaskUpdateHeadOptions()
	PinInteriorInMemory(94722)
	while IsInteriorReady(94722) ~= 1 or HasModelLoaded(model) do
		Wait(100)
	end
	TriggerEvent('es_admin:freezePlayer', true)
	Wait(1000)
	TriggerEvent('es_admin:freezePlayer', false)
	TriggerEvent('es_admin:teleportUser', vector3(402.55,-996.37,-100.01))
	Wait(10)
	TriggerEvent('es_admin:teleportUser', vector3(402.55,-996.37,-100.01))
	SetEntityHeading(PlayerPedId(),f(180.0))
	TriggerEvent('es_admin:freezePlayer', false)
	FreezeEntityPosition(PlayerPedId(), true)
	SetEntityVisible(PlayerPedId(),true)
	CreateCharacterCamera() 
	Citizen.Wait(1000)
	SetNuiFocus(isInCharacterMode,isInCharacterMode)
	SendNUIMessage({ CharacterMode = isInCharacterMode, CharacterMode2 = not isInCharacterMode, CharacterMode3 = not isInCharacterMode })
	EndFade()
end

function refreshDefaultCharacter()
	SetPedDefaultComponentVariation(PlayerPedId())
	ClearAllPedProps(PlayerPedId())
    ClearPedDecorations(PlayerPedId())
	if GetEntityModel(PlayerPedId()) == GetHashKey("mp_m_freemode_01") then
		SetPedComponentVariation(PlayerPedId(),1,-1,0,2) 
		SetPedComponentVariation(PlayerPedId(),3,15,0,2) 
		SetPedComponentVariation(PlayerPedId(),4,61,0,2) 
		SetPedComponentVariation(PlayerPedId(),5,-1,0,2) 
		SetPedComponentVariation(PlayerPedId(),6,16,0,2) 
		SetPedComponentVariation(PlayerPedId(),7,-1,0,2) 
		SetPedComponentVariation(PlayerPedId(),8,15,0,2) 
		SetPedComponentVariation(PlayerPedId(),9,-1,0,2) 
		SetPedComponentVariation(PlayerPedId(),10,-1,0,2) 
		SetPedComponentVariation(PlayerPedId(),11,15,0,2) 
		SetPedPropIndex(PlayerPedId(),2,-1,0,2) 
		SetPedPropIndex(PlayerPedId(),6,-1,0,2) 
		SetPedPropIndex(PlayerPedId(),7,-1,0,2) 
	else
		SetPedComponentVariation(PlayerPedId(),1,-1,0,2) 
		SetPedComponentVariation(PlayerPedId(),3,15,0,2) 
		SetPedComponentVariation(PlayerPedId(),4,15,0,2) 
		SetPedComponentVariation(PlayerPedId(),5,-1,0,2) 
		SetPedComponentVariation(PlayerPedId(),6,5,0,2) 
		SetPedComponentVariation(PlayerPedId(),7,-1,0,2) 
		SetPedComponentVariation(PlayerPedId(),8,7,0,2) 
		SetPedComponentVariation(PlayerPedId(),9,-1,0,2) 
		SetPedComponentVariation(PlayerPedId(),10,-1,0,2) 
		SetPedComponentVariation(PlayerPedId(),11,5,0,2) 
		SetPedPropIndex(PlayerPedId(),2,-1,0,2) 
		SetPedPropIndex(PlayerPedId(),6,-1,0,2) 
		SetPedPropIndex(PlayerPedId(),7,-1,0,2) 
	end
end

function changeGender(model)
	local mhash = GetHashKey(model)
	while not HasModelLoaded(mhash) do
		RequestModel(mhash)
		Citizen.Wait(10)
	end
	if HasModelLoaded(mhash) then
		SetPlayerModel(PlayerId(),mhash)
		SetPedMaxHealth(PlayerPedId(),200)
		SetEntityHealth(PlayerPedId(),200)
		SetModelAsNoLongerNeeded(mhash)
		SetEntityVisible(PlayerPedId(),true)
	end
end

function FadeOutNet()
	if continuousFadeOutNetwork then 
		for _, id in ipairs(GetActivePlayers()) do
			if id ~= PlayerId() then
				NetworkFadeOutEntity(GetPlayerPed(id),false)
			end
		end
		SetTimeout(0, FadeOutNet)
	end
end

RegisterNUICallback('cDoneSave',function(data,cb)
	StartFade()
	isInCharacterMode = false
	SetNuiFocus(isInCharacterMode,isInCharacterMode)
	SendNUIMessage({ CharacterMode = isInCharacterMode, CharacterMode2 = isInCharacterMode, CharacterMode3 = isInCharacterMode })
	local coord = lastcoord or vector3(-259.52, -976.68, 31.22)
	SetEntityCoords(PlayerPedId(), coord.x, coord.y, coord.z - 1)
	SetEntityHeading(PlayerPedId(),f(158.62))
	FreezeEntityPosition(PlayerPedId(), false)
	continuousFadeOutNetwork = false
	for _, id in ipairs(GetActivePlayers()) do
		if id ~= PlayerId() and NetworkIsPlayerActive(id) then
			if GetPlayerPed(id) ~= PlayerPedId() then
				NetworkFadeInEntity(GetPlayerPed(id),true)
			end
		end
	end
	TriggerEvent('skinchanger:loadSkin', currentCharacterMode)
	local relatedTable = Config.DefaultClothes[GetEntityModel(PlayerPedId())]
	local choosenClothe = math.random(1, #relatedTable)
	local cArray = json.decode(relatedTable[choosenClothe])
	for k,v in pairs(cArray) do
		currentCharacterMode[k] = v
		TriggerEvent('skinchanger:change', k, v)
	end
	local sosShirs = {['mask_1'] = -1,['mask_2'] = 0,['bproof_1'] = -1,	['bproof_2'] = 0,	['chain_1'] = -1,['chain_2'] = 0,['bags_1'] = -1,['bags_2'] = 0,['helmet_1'] = -1,	['helmet_2'] = 0,	['glasses_1'] = -1,	['glasses_2'] = 0,	['watches_1'] = -1,	['watches_2'] = 0,	['bracelets_1'] = -1,	['bracelets_2'] = 0} 
	for k,v in pairs(sosShirs) do
		currentCharacterMode[k] = v
		TriggerEvent('skinchanger:change', k, v)
	end
    TriggerServerEvent('esx_skin:save', currentCharacterMode)
    local playerName = characterNome ..'_'.. characterSobrenome
    TriggerServerEvent('db:updateUser', { playerName = playerName})
    TriggerServerEvent('es:newName', playerName)
    CreateCameraOnTop()
	EndFade()
	ReadToPlay()
end)

RegisterNUICallback('cChangeHeading',function(data,cb)
	SetEntityHeading(PlayerPedId(),f(data.camRotation))
	cb('ok')
end)

RegisterNUICallback('ChangeGender',function(data,cb)
	currentCharacterMode.sex = tonumber(data.gender)
	if tonumber(data.gender) == 1 then
		changeGender("mp_f_freemode_01")
	else
		changeGender("mp_m_freemode_01")
	end
	refreshDefaultCharacter()
	TaskUpdateSkinOptions()
	TaskUpdateFaceOptions()
	TaskUpdateHeadOptions()
	cb('ok')
end)

RegisterNUICallback('UpdateSkinOptions',function(data,cb)
	currentCharacterMode.dad = data.dad
	currentCharacterMode.mom = data.mom
	currentCharacterMode.skin_md_weight = data.skin_md_weight 
	currentCharacterMode.face_md_weight = data.face_md_weight * 100 
	characterNome = data.characterNome
	characterSobrenome = data.characterSobrenome
	TaskUpdateSkinOptions()
	cb('ok')
end)

function TaskUpdateSkinOptions()
	local data = currentCharacterMode
	local face_weight = 		(data['face_md_weight'] / 100) + 0.0
	SetPedHeadBlendData(PlayerPedId(), data['dad'], data['mom'], 0, data['skin_md_weight'], 0, 0, face_weight, 0, 0, false)
end

RegisterNUICallback('UpdateFaceOptions',function(data,cb)
	currentCharacterMode.eye_color = data.eye_color
	currentCharacterMode.eyebrows_5 = data.eyebrows_5 * 10
	currentCharacterMode.eyebrows_6 = data.eyebrows_6 * 10
	currentCharacterMode.nose_1 = data.nose_1 * 10
	currentCharacterMode.nose_2 = data.nose_2 * 10
	currentCharacterMode.nose_3 = data.nose_3 * 10
	currentCharacterMode.nose_4 = data.nose_4 * 10
	currentCharacterMode.nose_5 = data.nose_5 * 10
	currentCharacterMode.nose_6 = data.nose_6 * 10
	currentCharacterMode.cheeks_1 = data.cheeks_1 * 10
	currentCharacterMode.cheeks_2 = data.cheeks_2 * 10
	currentCharacterMode.cheeks_3 = data.cheeks_3 * 10
	currentCharacterMode.lip_thickness = data.lip_thickness * 10
	currentCharacterMode.jaw_1 = data.jaw_1 * 10
	currentCharacterMode.jaw_2 = data.jaw_2 * 10
	currentCharacterMode.chin_1 = data.chin_1 * 10
	currentCharacterMode.chin_2 = data.chin_2 * 10
	currentCharacterMode.chin_3 = data.chin_3 * 10
	currentCharacterMode.chin_4 = data.chin_4 * 10
	currentCharacterMode.neck_thickness = data.neck_thickness * 10
	TaskUpdateFaceOptions()
	cb('ok')
end)

function TaskUpdateFaceOptions()
	local ped = PlayerPedId()
	local data = currentCharacterMode
	SetPedEyeColor(ped,data.eye_color)
	SetPedFaceFeature(ped,6,data.eyebrows_5/10)
	SetPedFaceFeature(ped,7,data.eyebrows_6/10)
	SetPedFaceFeature(ped,0,data.nose_1/10)
	SetPedFaceFeature(ped,1,data.nose_2/10)
	SetPedFaceFeature(ped,2,data.nose_3/10)
	SetPedFaceFeature(ped,3,data.nose_4/10)
	SetPedFaceFeature(ped,4,data.nose_5/10)
	SetPedFaceFeature(ped,5,data.nose_6/10)
	SetPedFaceFeature(ped,8,data.cheeks_1/10)
	SetPedFaceFeature(ped,9,data.cheeks_2/10)
	SetPedFaceFeature(ped,10,data.cheeks_3/10)
	SetPedFaceFeature(ped,12,data.lip_thickness/10)
	SetPedFaceFeature(ped,13,data.jaw_1/10)
	SetPedFaceFeature(ped,14,data.jaw_2/10)
	SetPedFaceFeature(ped,15,data.chin_1/10)
	SetPedFaceFeature(ped,16,data.chin_2/10)
	SetPedFaceFeature(ped,17,data.chin_3/10)
	SetPedFaceFeature(ped,18,data.chin_4/10)
	SetPedFaceFeature(ped,19,data.neck_thickness/10)
end

RegisterNUICallback('UpdateHeadOptions',function(data,cb)
	currentCharacterMode.hair_1 = data.hair_1
	currentCharacterMode.hair_2 = 0
	currentCharacterMode.hair_color_1 = data.hair_color_1
	currentCharacterMode.hair_color_2 = data.hair_color_2
	currentCharacterMode.eyebrows_1 = data.eyebrows_1
	currentCharacterMode.eyebrows_2 = 10
	currentCharacterMode.eyebrows_3 = data.eyebrows_3
	currentCharacterMode.eyebrows_4 = data.eyebrows_3
	currentCharacterMode.beard_1 = data.beard_1
	currentCharacterMode.beard_2 = 10
	currentCharacterMode.beard_3 = data.beard_3
	currentCharacterMode.beard_4 = data.beard_3
	currentCharacterMode.chest_1 = data.chest_1
	currentCharacterMode.chest_2 = 10
	currentCharacterMode.chest_3 = data.chest_3
	currentCharacterMode.blush_1 = data.blush_1
	currentCharacterMode.blush_2 = 10
	currentCharacterMode.blush_3 = data.blush_3
	currentCharacterMode.lipstick_1 = data.lipstick_1
	currentCharacterMode.lipstick_2 = 10
	currentCharacterMode.lipstick_3 = data.lipstick_3
	currentCharacterMode.lipstick_4 = data.lipstick_3
	currentCharacterMode.blemishes_1 = data.blemishes_1
	currentCharacterMode.blemishes_2 = 10
	currentCharacterMode.age_1 = data.age_1
	currentCharacterMode.age_2 = 10
	currentCharacterMode.complexion_1 = data.complexion_1
	currentCharacterMode.complexion_2 = 10
	currentCharacterMode.sun_1 = data.sun_1
	currentCharacterMode.sun_2 = 10
	currentCharacterMode.moles_1 = data.moles_1
	currentCharacterMode.moles_2 = 10
	currentCharacterMode.makeup_1 = data.makeup_1
	currentCharacterMode.makeup_2 = 10
	currentCharacterMode.makeup_3 = 0
	currentCharacterMode.makeup_4 = 0
	TaskUpdateHeadOptions()
	cb('ok')
end)

function TaskUpdateHeadOptions()
	local ped = PlayerPedId()
	local data = currentCharacterMode
	SetPedComponentVariation(ped,2,data.hair_1,0,0)
	SetPedHairColor(ped,data.hair_color_1,data.hair_color_2)
	SetPedHeadOverlay(ped,2,data.eyebrows_1,0.99)
	SetPedHeadOverlayColor(ped,2,1,data.eyebrows_3,data.eyebrows_3)
	SetPedHeadOverlay(ped,1,data.beard_1,0.99)
	SetPedHeadOverlayColor(ped,1,1,data.beard_3,data.beard_3)
	SetPedHeadOverlay(ped,10,data.chest_1,0.99)
	SetPedHeadOverlayColor(ped,10,1,data.chest_3,data.chest_3)
	SetPedHeadOverlay(ped,5,data.blush_1,0.99)
	SetPedHeadOverlayColor(ped,5,2,data.blush_3,data.blush_3)
	SetPedHeadOverlay(ped,8,data.lipstick_1,0.99)
	SetPedHeadOverlayColor(ped,8,2,data.lipstick_3,data.lipstick_3)
	SetPedHeadOverlay(ped,0,data.blemishes_1,0.99)
	SetPedHeadOverlay(ped,3,data.age_1,0.99)
	SetPedHeadOverlay(ped,6,data.complexion_1,0.99)
	SetPedHeadOverlay(ped,7,data.sun_1,0.99)
	SetPedHeadOverlay(ped,9,data.moles_1,0.99)
	SetPedHeadOverlay(ped,4,data.makeup_1,0.99)
	SetPedHeadOverlayColor(ped,4,0,0,0)
end

RegisterNetEvent('skincreator:newCharst')
AddEventHandler('skincreator:newCharst', function ()
	TriggerCreateCharacter()
end)
