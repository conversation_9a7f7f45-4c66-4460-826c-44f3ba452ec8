﻿html {
  height: 100%;
  font-family: Segoe UI;
  font-weight: 100;
  font-size: 14pt;
  color: #555;
  text-align: center;
}

.b1 {
  width: 20vw;
  height: 100vh;
  /*background: #DDD;*/
  background: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  right: 0;
}

.b1-title {
  width: 100%;
  /*background: mediumorchid;*/
  font-size: 30px;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
  background: rgb(255, 0, 136);
  background: linear-gradient(45deg,  rgba(255, 0, 136) 0%,rgb(253, 57, 161) 30%,rgb(254, 73, 169) 32%,rgb(254, 90, 177) 70%,rgb(255, 104, 184) 72%,rgb(255, 114, 188) 88%,rgb(255, 125, 194) 90%,rgb(255, 138, 200) 100%);

  color: white;
  padding: 3vh 0;
  /*font-size: 2vh;*/
}

.fm-b {
  display: flex;
  font-size: 2.5vh;
}

.fm-b_arrow {
  width: 8vh;
  padding-top: 1.0vh;
  color: mediumorchid;
  transition: 0.3s;
}

.fm-b_arrow:hover {
  background: mediumorchid;
  color: white;
}

.fm-b_main {
  width: 100%;
  line-height: 1vh;
  padding-top: 1.5vh;
  color: mediumorchid;
}

.fm-b_main p {
  font-size: 1.2vh;
  color: #ccc;
}

.fm-input-b {
  height: 20.5vh;
  position: relative;
  /*background: #ddd;*/
  background: rgba(0, 0, 0, 0.0);

}

.fm-input-b input:focus {
  outline: none;
}

.fm-input-b input:before {
  content: "";
  background: white;
  width: 20vh;
  height: 1px;
  position: absolute;
  top: 50%;
  left: 0;
}

.fm-input-b input[type=range] {
  -webkit-appearance: none;
  width: 20vh;
  transform: rotate(90deg);
  position: relative;
  top: 2.5vh;
  background: transparent;
}

.fm-input-b input[type=range]::-webkit-slider-runnable-track {
  height: 15vh;
  /*background: #ddd;*/
  background: rgb(255, 0, 136);
  background: linear-gradient(45deg,  rgba(255, 0, 136) 0%,rgb(253, 57, 161) 30%,rgb(254, 73, 169) 32%,rgb(254, 90, 177) 70%,rgb(255, 104, 184) 72%,rgb(255, 114, 188) 88%,rgb(255, 125, 194) 90%,rgb(255, 138, 200) 100%);
  border-radius: 50px;
}

.fm-input-b input[type=range]::-webkit-slider-thumb {
  height: 5vh;
  width: 5vh;
  background: white;
  -webkit-appearance: none;
  margin-top: 5vh;
  border-radius: 50%;
}

.fm-b_main input:focus {
  outline: none;
}

.fm-b_main input:before {
  content: "";
  background: white;
  width: 20vh;
  height: 1px;
  position: absolute;
  top: 50%;
  left: 0;
}

.fm-b_main input[type=range] {
  -webkit-appearance: none;
  width: 20vh;
  position: relative;
  background: transparent;
}

.fm-b_main input[type=range]::-webkit-slider-runnable-track {
  height: 5vh;
  /*background: #ddd;*/
    background: rgb(255, 0, 136);
  background: linear-gradient(45deg,  rgba(255, 0, 136) 0%,rgb(253, 57, 161) 30%,rgb(254, 73, 169) 32%,rgb(254, 90, 177) 70%,rgb(255, 104, 184) 72%,rgb(255, 114, 188) 88%,rgb(255, 125, 194) 90%,rgb(255, 138, 200) 100%);
  border-radius: 50px;
}

.fm-b_main input[type=range]::-webkit-slider-thumb {
  height: 5vh;
  width: 5vh;
  background: white;
  -webkit-appearance: none;
  border-radius: 50%;
}

.b1-done {
  background: forestgreen;
  padding: 1vh 0;
 position: absolute;
 left: 0;
 bottom: 0;
 transition: 0.3s;
}

.b1-done:hover {
  background: mediumorchid;
}

.b1-done {
  /*background: forestgreen;*/
  background: rgb(255, 0, 136);
  padding: 1vh 0;
 position: absolute;
 left: 0;
 bottom: 0;
 transition: 0.3s;
}

.b1-done:hover {
  background: mediumorchid;
}

.b1-back {
  background: rgb(255, 0, 136);
  padding: 1vh 0;
 position: absolute;
 left: 0;
 bottom: 60px;
 transition: 0.3s;
}

.b1-back:hover {
  background: mediumorchid;
}

.b1-face-item {
	height: 4vh;
	position: relative;
	margin-bottom: 0.1vh;
}

.b1-face-item > input:focus {
  outline: none;
}

.b1-face-item > input:before {
  content: "";
  background: white;
  width: 18vw;
  height: 1px;
  position: absolute;
  top: 50%;
  left: 0;
}

.b1-face-item > input[type=range] {
  -webkit-appearance: none;
  width: 18vw;
  position: relative;
}

.b1-face-item > input[type=range]::-webkit-slider-runnable-track {
  height: 2vh;
  /*background: #ddd;*/
  background: linear-gradient(45deg,  rgba(255, 0, 136) 0%,rgb(253, 57, 161) 30%,rgb(254, 73, 169) 32%,rgb(254, 90, 177) 70%,rgb(255, 104, 184) 72%,rgb(255, 114, 188) 88%,rgb(255, 125, 194) 90%,rgb(255, 138, 200) 100%);
}

.b1-face-item > input[type=range]::-webkit-slider-thumb {
  height: 2vh;
  width: 5vh;
  background: white;
  -webkit-appearance: none;
  background: url(range.png) center;
  background-size: cover;
}

.b1-face-item > p {
	display: block;
  font-size: 1.3vh;
  color: #ccc;
  position: absolute;
  bottom: -1.0vh;
  left: 0;
  text-align: center;
  width: 100%;
}
