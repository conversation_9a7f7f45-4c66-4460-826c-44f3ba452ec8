ESX = nil

TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- Wait for ESX to be ready
Citizen.CreateThread(function()
    while ESX == nil do
        Citizen.Wait(0)
    end
    print("^2[STG Clothing] ESX loaded successfully^7")
end)

-- ESX Compatibility Functions
function GetPlayerCash(xPlayer)
    if not xPlayer then return 0 end

    if xPlayer.getMoney then
        return xPlayer.getMoney()
    elseif xPlayer.money then
        return xPlayer.money
    else
        return 0
    end
end

function GetPlayerBank(xPlayer)
    if not xPlayer then return 0 end

    if xPlayer.getAccount then
        local bankAccount = xPlayer.getAccount('bank')
        if bankAccount then
            return bankAccount.money or 0
        end
    end

    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                return xPlayer.accounts[i].money or 0
            end
        end
    end

    return 0
end

function RemovePlayerCash(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    local currentMoney = GetPlayerCash(xPlayer)
    if currentMoney < amount then return false end

    if xPlayer.removeMoney then
        local result = xPlayer.removeMoney(amount)
        return result ~= false
    elseif xPlayer.money then
        xPlayer.money = xPlayer.money - amount
        return true
    end

    return false
end

function RemovePlayerBank(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    local currentMoney = GetPlayerBank(xPlayer)
    if currentMoney < amount then return false end

    if xPlayer.removeAccountMoney then
        local result = xPlayer.removeAccountMoney('bank', amount)
        return result ~= false
    end
    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                if xPlayer.accounts[i].money >= amount then
                    xPlayer.accounts[i].money = xPlayer.accounts[i].money - amount
                    return true
                end
                break
            end
        end
    end

    return false
end

function AddPlayerCash(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    if xPlayer.addMoney then
        xPlayer.addMoney(amount)
        return true
    elseif xPlayer.money then
        xPlayer.money = xPlayer.money + amount
        return true
    end

    return false
end

function AddPlayerBank(xPlayer, amount)
    if not xPlayer or not amount or amount <= 0 then return false end

    if xPlayer.addAccountMoney then
        xPlayer.addAccountMoney('bank', amount)
        return true
    end
    if xPlayer.accounts then
        for i=1, #xPlayer.accounts do
            if xPlayer.accounts[i].name == 'bank' then
                xPlayer.accounts[i].money = xPlayer.accounts[i].money + amount
                return true
            end
        end
    end

    return false
end


ESX.RegisterServerCallback('stg_clothing:getWardrobe', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.query("SELECT * FROM stg_clothing WHERE owner = ?", {
        xPlayer.identifier
    }, function(result)
        cb(result)
    end)
end)


ESX.RegisterServerCallback('stg_clothing:getOutfit', function(source, cb, id)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.query("SELECT * FROM stg_clothing WHERE id = ?", {
        id
    }, function(result)
        cb(result[1])
    end)
end)


-- Money check callback (compatible with esx_eden_clotheshop)
ESX.RegisterServerCallback('stg_clothing:getMoney', function(source, cb, type, money)
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        print("^1[STG Clothing] ✗ Could not get player from ESX^7")
        cb(false)
        return
    end

    if type == "cash" then
        local playerMoney = GetPlayerCash(xPlayer)
        if playerMoney >= money then
            local success = RemovePlayerCash(xPlayer, money)
            cb(success)
        else
            cb(false)
        end
    else
        local bankMoney = GetPlayerBank(xPlayer)
        if bankMoney >= money then
            local success = RemovePlayerBank(xPlayer, money)
            cb(success)
        else
            cb(false)
        end
    end
end)

-- Check money callback (like esx_eden_clotheshop)
ESX.RegisterServerCallback('esx_eden_clotheshop:checkMoney', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)

    if xPlayer and xPlayer.getMoney() >= 50 then -- Default price
        cb(true)
    else
        cb(false)
    end
end)


RegisterNetEvent('stg_clothing:saveOutfit')
AddEventHandler('stg_clothing:saveOutfit', function(name, skin)
    local xPlayer = ESX.GetPlayerFromId(source)
    MySQL.insert("INSERT INTO stg_clothing (owner, skin, name) VALUES (?, ?, ?)", {
        xPlayer.identifier,
        json.encode(skin),
        name
    }, function(insertId)

    end)
end)


RegisterNetEvent('stg_clothing:deleteWardrobe')
AddEventHandler('stg_clothing:deleteWardrobe', function(id)
    local src = source
    MySQL.query("DELETE FROM `stg_clothing` WHERE `id` = ?", {
        id
    }, function(affectedRows)
        TriggerClientEvent('stg_clothing:refreshWardrobe', src)
    end)
end)




-- Get skin from datastore (compatible with esx_eden_clotheshop)
ESX.RegisterServerCallback('stg_clothing:getSkin', function(source, cb, method)
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        print("^1[STG Clothing] ✗ Could not get player from ESX^7")
        cb(nil)
        return
    end

    TriggerEvent('esx_datastore:getDataStore', 'property', xPlayer.identifier, function(store)
        local dressing = store.get('dressing')

        if dressing and #dressing > 0 then
            -- Find STG outfit or use last outfit
            local targetOutfit = nil

            -- Look for STG outfit first
            for i = #dressing, 1, -1 do
                if dressing[i].label and string.find(dressing[i].label, 'STG_') then
                    targetOutfit = dressing[i]
                    break
                end
            end

            -- If no STG outfit, use last outfit
            if not targetOutfit then
                targetOutfit = dressing[#dressing]
            end

            if targetOutfit and targetOutfit.skin then
                print("^2[STG Clothing] ✓ Skin loaded from datastore^7")
                cb({skin = json.encode(targetOutfit.skin)})
                return
            end
        end

        print("^3[STG Clothing] No skin found in datastore^7")
        cb(nil)
    end)
end)

-- Get player outfits (like esx_eden_clotheshop)
ESX.RegisterServerCallback('esx_eden_clotheshop:getPlayerDressing', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)

    TriggerEvent('esx_datastore:getDataStore', 'property', xPlayer.identifier, function(store)
        local count = store.count('dressing')
        local labels = {}

        for i=1, count, 1 do
            local entry = store.get('dressing', i)
            table.insert(labels, entry.label)
        end

        cb(labels)
    end)
end)

-- Get specific outfit (like esx_eden_clotheshop)
ESX.RegisterServerCallback('esx_eden_clotheshop:getPlayerOutfit', function(source, cb, num)
    local xPlayer = ESX.GetPlayerFromId(source)

    TriggerEvent('esx_datastore:getDataStore', 'property', xPlayer.identifier, function(store)
        local outfit = store.get('dressing', num)
        cb(outfit.skin)
    end)
end)


-- Save skin to datastore (compatible with esx_eden_clotheshop)
RegisterServerEvent("stg_clothing:saveSkin")
AddEventHandler("stg_clothing:saveSkin", function(model, skin)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if not xPlayer then
        print("^1[STG Clothing] ✗ Could not get player from ESX^7")
        return
    end

    if not skin then
        print("^1[STG Clothing] ✗ Invalid skin data^7")
        return
    end

    TriggerEvent('esx_datastore:getDataStore', 'property', xPlayer.identifier, function(store)
        local dressing = store.get('dressing')

        if dressing == nil then
            dressing = {}
        end

        -- Decode skin data
        local success, skinData = pcall(json.decode, skin)
        if not success then
            print("^1[STG Clothing] ✗ Failed to decode skin JSON^7")
            return
        end

        -- Remove old STG outfits to avoid duplicates
        for i = #dressing, 1, -1 do
            if dressing[i].label and string.find(dressing[i].label, 'STG_Auto_Save') then
                table.remove(dressing, i)
            end
        end

        -- Add new outfit
        table.insert(dressing, {
            label = 'STG_Auto_Save_' .. os.time(),
            skin = skinData
        })

        -- Keep only last 10 outfits
        if #dressing > 10 then
            table.remove(dressing, 1)
        end

        store.set('dressing', dressing)
        print("^2[STG Clothing] ✓ Skin saved to datastore successfully^7")
    end)
end)

-- Save outfit (like esx_eden_clotheshop)
RegisterServerEvent('esx_eden_clotheshop:saveOutfit')
AddEventHandler('esx_eden_clotheshop:saveOutfit', function(label, skin)
    local xPlayer = ESX.GetPlayerFromId(source)

    TriggerEvent('esx_datastore:getDataStore', 'property', xPlayer.identifier, function(store)
        local dressing = store.get('dressing')

        if dressing == nil then
            dressing = {}
        end

        table.insert(dressing, {
            label = label,
            skin = skin
        })

        store.set('dressing', dressing)
    end)
end)

-- Delete outfit (like esx_eden_clotheshop)
RegisterServerEvent('esx_eden_clotheshop:deleteOutfit')
AddEventHandler('esx_eden_clotheshop:deleteOutfit', function(label)
    local xPlayer = ESX.GetPlayerFromId(source)

    TriggerEvent('esx_datastore:getDataStore', 'property', xPlayer.identifier, function(store)
        local dressing = store.get('dressing')

        if dressing == nil then
            dressing = {}
        end

        table.remove(dressing, label)
        store.set('dressing', dressing)
    end)
end)

-- Pay for clothing (like esx_eden_clotheshop)
RegisterServerEvent('esx_eden_clotheshop:pay')
AddEventHandler('esx_eden_clotheshop:pay', function()
    local xPlayer = ESX.GetPlayerFromId(source)
    local price = 50 -- Default price

    xPlayer.removeMoney(price)
    TriggerClientEvent('esx:showNotification', source, 'You paid $' .. price)
end)




