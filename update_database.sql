-- Update database for STG Clothing to use users table
-- آپدیت دیتابیس برای استفاده از جدول users

-- اطمینان از وجود فیلد skin در جدول users (اگر وجود نداره اضافه میشه)
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `skin` longtext DEFAULT NULL;

-- حذف جدول playerskins اگر وجود داره (اختیاری)
-- DROP TABLE IF EXISTS `playerskins`;

-- نکته: اگر قبلاً از جدول playerskins استفاده میکردید و میخواهید دیتاها رو منتقل کنید:
-- UPDATE users u 
-- JOIN playerskins p ON u.identifier = p.identifier 
-- SET u.skin = p.skin 
-- WHERE p.active = 1;
